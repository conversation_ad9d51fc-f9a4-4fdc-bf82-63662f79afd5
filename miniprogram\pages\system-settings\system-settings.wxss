/* ===================================================================
 * system-settings.wxss - 系统设置页面样式文件
 *
 * 功能说明：
 * 这是微信小程序的系统设置页面样式文件，负责定义页面的视觉呈现
 * 包含卡片布局、表单输入、按钮样式等组件的样式定义
 *
 * 设计原则：
 * 1. 现代化设计：使用圆角、阴影、渐变等现代UI元素
 * 2. 响应式布局：适配不同屏幕尺寸的设备
 * 3. 用户体验：清晰的视觉层次和交互反馈
 * 4. 品牌一致性：使用统一的颜色和字体规范
 *
 * 技术特点：
 * - 使用rpx单位：微信小程序的响应式像素单位，自动适配不同设备
 * - CSS3特性：transition动画、box-shadow阴影、border-radius圆角
 * - Flexbox布局：现代的弹性盒子布局，类似于CSS Grid
 * - 媒体查询：@media实现响应式设计
 *
 * 与传统CSS对比：
 * - rpx vs px：rpx会根据设备屏幕宽度自动缩放，1rpx = 屏幕宽度/750
 * - 小程序特有：page选择器用于设置页面根元素样式
 * - 组件样式：.t-开头的类名是TDesign组件库的样式类
 * ================================================================= */

/* ===================================================================
 * 1. 页面基础样式 - Page Foundation Styles
 *
 * 设置页面的基础外观，包括背景色和主容器样式
 * 这些样式影响整个页面的基础视觉效果
 * ================================================================= */

/* 页面根元素样式 - 设置整个页面的背景色 */
page {
  background-color: #f8f8f8; /* 浅灰色背景，提供舒适的视觉体验，类似iOS设置页面 */
}

/* 主容器样式 - 页面内容的包装容器 */
.container {
  min-height: 100vh;        /* 最小高度：100vh确保容器至少占满整个视口高度 */
  box-sizing: border-box;   /* 盒模型：padding和border包含在width内，避免布局溢出 */
  position: relative;       /* 相对定位：为固定按钮提供定位基准 */
}



/* 页面内容包装器 */
.content-wrapper {
  padding: 32rpx; /* 内边距：统一的32rpx内边距 */
}

/* ===================================================================
 * 2. 设置卡片样式 - Setting Card Styles
 *
 * 卡片是页面的主要内容容器，每个功能模块使用一个卡片
 * 采用现代化的卡片设计，包含阴影、圆角、悬停效果等
 * ================================================================= */

/* 设置卡片主体样式 - 现代化卡片设计 */
.setting-card {
  background-color: #ffffff;                    /* 白色背景，与页面背景形成对比 */
  border-radius: 24rpx;                         /* 圆角：24rpx ≈ 12px，现代化的圆角设计 */
  margin-bottom: 32rpx;                         /* 下边距：卡片之间的间隔 */
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08); /* 阴影：轻微的阴影效果，增加层次感 */
  padding: 32rpx;                               /* 内边距：为卡片内容提供空间 */
  transition: all 0.3s ease;                    /* 过渡动画：0.3秒的平滑过渡效果 */
  width: 100%;                                  /* 宽度：占满父容器宽度 */
  box-sizing: border-box;                       /* 盒模型：确保padding不会增加总宽度 */
}

/* 卡片悬停效果 - 提供交互反馈（注意：小程序中hover效果有限） */
.setting-card:hover {
  transform: translateY(-4rpx);                 /* Y轴位移：向上移动4rpx，营造浮起效果 */
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1); /* 增强阴影：悬停时阴影更明显 */
}

/* ===================================================================
 * 3. 卡片头部样式 - Card Header Styles
 *
 * 卡片头部包含图标和标题，用于标识每个功能模块
 * 使用Flexbox布局实现图标和文字的对齐
 * ================================================================= */

/* 卡片头部容器 */
.card-header {
  display: flex;                    /* Flexbox布局：水平排列图标和标题 */
  align-items: center;              /* 垂直居中：图标和文字垂直对齐 */
  padding-bottom: 24rpx;            /* 下内边距：与卡片内容保持距离 */
  margin-bottom: 24rpx;             /* 下外边距：与下方内容的间隔 */
  border-bottom: 1rpx solid #f0f0f0; /* 下边框：浅灰色分割线，分离头部和内容 */
}

/* 头部图标样式 - TDesign图标组件的自定义样式 */
.card-header .t-icon {
  color: #0052d9; /* 主题蓝色：与品牌色保持一致，突出重要性 */
}

/* 卡片标题样式 */
.card-title {
  font-size: 32rpx;     /* 字体大小：32rpx ≈ 16px，标题级别的字体大小 */
  font-weight: 600;     /* 字体粗细：600 = semi-bold，突出标题重要性 */
  color: #333333;       /* 字体颜色：深灰色，确保良好的可读性 */
  margin-left: 16rpx;   /* 左边距：与图标保持适当间距 */
}

/* ===================================================================
 * 4. 卡片内容样式 - Card Body Styles
 *
 * 卡片内容区域包含具体的设置项和输入控件
 * 使用Flexbox布局实现垂直排列和间距控制
 * ================================================================= */

/* 卡片内容容器 */
.card-body {
  display: flex;        /* Flexbox布局：垂直排列内容项 */
  flex-direction: column; /* 主轴方向：垂直方向，内容项从上到下排列 */
  gap: 16rpx;           /* 间距：子元素之间的统一间距，CSS Grid的gap属性 */
}

/* ===================================================================
 * 5. 设置项布局 - Setting Item Layout
 *
 * 每个设置项包含标签和对应的控件（显示值、输入框等）
 * 使用Flexbox实现左右布局和对齐
 * ================================================================= */

/* 设置项容器 - 左右布局的设置项 */
.setting-item {
  display: flex;                /* Flexbox布局：水平排列标签和控件 */
  justify-content: space-between; /* 主轴对齐：两端对齐，标签在左，控件在右 */
  align-items: center;          /* 交叉轴对齐：垂直居中对齐 */
  padding: 24rpx 0;             /* 上下内边距：为设置项提供垂直空间 */
  min-height: 60rpx;            /* 最小高度：确保设置项有足够的点击区域 */
}

/* 设置项分割线 - 除最后一个外，其他设置项都有下边框 */
.setting-item:not(:last-child) {
  border-bottom: 1rpx solid #f5f5f5; /* 下边框：浅灰色分割线，分离不同设置项 */
}

/* 设置项标签样式 */
.setting-label {
  font-size: 28rpx;   /* 字体大小：28rpx ≈ 14px，正文级别的字体大小 */
  color: #333333;     /* 字体颜色：深灰色，确保可读性 */
  font-weight: 500;   /* 字体粗细：500 = medium，适中的字体粗细 */
}

/* ===================================================================
 * 6. 时间设置专用样式 - Time Setting Specific Styles
 *
 * 预约取消时间设置的专用样式，包含时间显示和输入控件
 * 这部分样式专门为时间设置功能定制
 * ================================================================= */

/* 时间设置值显示容器 - 显示当前设置的时间值 */
.time-setting-value {
  display: flex;          /* Flexbox布局：水平排列时间数字和单位 */
  align-items: baseline;  /* 基线对齐：数字和单位按基线对齐，视觉效果更好 */
  gap: 8rpx;             /* 间距：数字和单位之间的小间距 */
}

/* 时间数值样式 - 突出显示的时间数字 */
.time-value {
  font-size: 36rpx;   /* 字体大小：36rpx ≈ 18px，大号字体突出重要信息 */
  font-weight: 600;   /* 字体粗细：600 = semi-bold，强调数值重要性 */
  color: #0052d9;     /* 主题蓝色：与品牌色一致，突出关键数据 */
}

/* 时间单位样式 - 时间单位文字（小时/分钟） */
.time-unit {
  font-size: 24rpx; /* 字体大小：24rpx ≈ 12px，小号字体作为辅助信息 */
  color: #666666;   /* 中灰色：辅助信息的标准颜色，不抢夺主要信息的注意力 */
}

/* 输入行容器 - 时间输入控件的容器 */
.input-row {
  display: flex;              /* Flexbox布局：水平排列标签、输入框、后缀 */
  align-items: center;        /* 垂直居中：所有元素垂直对齐 */
  gap: 16rpx;                /* 间距：元素之间的统一间距 */
  margin-top: 16rpx;         /* 上边距：与上方内容保持距离 */
  background-color: #f8f9fa; /* 浅灰背景：区分输入区域和其他内容 */
  padding: 16rpx;            /* 内边距：为输入区域提供内部空间 */
  border-radius: 16rpx;      /* 圆角：16rpx ≈ 8px，柔和的圆角设计 */
}

/* 输入标签和后缀样式 - 输入框前后的说明文字 */
.input-label,
.input-suffix {
  font-size: 28rpx; /* 字体大小：28rpx ≈ 14px，与设置标签保持一致 */
  color: #666666;   /* 中灰色：辅助文字的标准颜色 */
}

/* 时间输入框样式 */
.time-input {
  flex: 1;          /* 弹性增长：占据剩余空间 */
  text-align: center; /* 文本居中：数字输入居中显示更美观 */
}

/* TDesign输入框控件的自定义样式 */
.time-input .t-input__control {
  text-align: center; /* 文本居中：确保输入内容居中显示 */
  font-weight: 500;   /* 字体粗细：500 = medium，突出输入内容 */
}

/* ===================================================================
 * 7. 联系信息输入样式 - Contact Info Input Styles
 *
 * 联系信息设置的输入控件样式，包含电话、地址、公告等
 * 这些输入框采用右对齐设计，符合表单设计规范
 * ================================================================= */

/* 设置输入框容器 - 右侧输入控件的容器 */
.setting-input {
  flex: 1;              /* 弹性增长：占据剩余空间 */
  margin-left: 32rpx;   /* 左边距：与标签保持适当距离 */
  text-align: right;    /* 文本右对齐：符合表单设计规范 */
}

/* 联系信息输入框的文本对齐 - TDesign组件的自定义样式 */
.contact-input .t-input__control,
.contact-textarea .t-textarea__control {
  text-align: right; /* 文本右对齐：与容器对齐方式保持一致 */
}

/* 联系信息多行文本框样式 - 门店公告的文本域 */
.contact-textarea {
  min-height: 120rpx;           /* 最小高度：确保有足够的输入空间 */
  padding: 16rpx;               /* 内边距：为文本提供内部空间 */
  background-color: #f8f9fa;    /* 浅灰背景：区分文本域和其他内容 */
  border-radius: 12rpx;         /* 圆角：12rpx ≈ 6px，柔和的圆角 */
  border: 1rpx solid #e5e5e5;   /* 边框：浅灰色边框，定义文本域边界 */
}

/* ===================================================================
 * 8. 描述文本样式 - Description Text Styles
 *
 * 设置项的说明文字样式，用于解释设置项的作用和注意事项
 * 采用较小的字体和浅色，作为辅助信息显示
 * ================================================================= */

/* 设置描述文字样式 - 说明性文字的通用样式 */
.setting-description {
  font-size: 24rpx;             /* 字体大小：24rpx ≈ 12px，小号字体用于说明文字 */
  color: #999999;               /* 浅灰色：辅助信息的标准颜色，不干扰主要内容 */
  line-height: 1.5;             /* 行高：1.5倍行高，提高文字可读性 */
  margin-top: 16rpx;            /* 上边距：与上方内容保持距离 */
  padding: 16rpx;               /* 内边距：为文字提供内部空间 */
  background-color: #f8f9fa;    /* 浅灰背景：区分说明文字和其他内容 */
  border-radius: 12rpx;         /* 圆角：12rpx ≈ 6px，与其他元素保持一致 */
}



/* ===================================================================
 * 10. 响应式设计 - Responsive Design
 *
 * 针对不同屏幕尺寸的适配样式，确保在小屏设备上的良好体验
 * 主要调整布局方向、间距和对齐方式
 *
 * 媒体查询说明：
 * @media (max-width: 750rpx) 对应小屏设备（约375px宽度）
 * 在小屏设备上，将水平布局改为垂直布局，提高可用性
 * ================================================================= */

@media (max-width: 750rpx) {
  /* 小屏设备下的容器样式调整 */
  .container {
    padding: 24rpx; /* 减少内边距：在小屏上节省空间 */
  }

  /* 小屏设备下的卡片样式调整 */
  .setting-card {
    padding: 24rpx; /* 减少卡片内边距：在小屏上节省空间 */
  }

  /* 小屏设备下的设置项布局调整 */
  .setting-item {
    flex-direction: column;  /* 布局方向：改为垂直布局，标签在上，控件在下 */
    align-items: flex-start; /* 对齐方式：左对齐，符合阅读习惯 */
    gap: 16rpx;             /* 间距：标签和控件之间的垂直间距 */
    padding: 20rpx 0;       /* 减少上下内边距：在小屏上节省空间 */
  }

  /* 小屏设备下的输入框样式调整 */
  .setting-input {
    margin-left: 0;    /* 移除左边距：垂直布局下不需要左边距 */
    width: 100%;       /* 全宽：输入框占满容器宽度 */
    text-align: left;  /* 左对齐：小屏设备上左对齐更易读 */
  }

  /* 小屏设备下的联系信息输入框对齐调整 */
  .contact-input .t-input__control,
  .contact-textarea .t-textarea__control {
    text-align: left; /* 左对齐：与容器对齐方式保持一致 */
  }
}

/* ===================================================================
 * 11. 多条公告样式 - Multiple Announcements Styles
 *
 * 多条公告功能的专用样式，包含SwipeCell、公告列表、操作按钮等
 * 采用现代化的卡片设计和滑动交互
 * ================================================================= */

/* 新增公告按钮样式 */
.add-announcement-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #0052d9;
  font-size: 28rpx;
  cursor: pointer;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  transition: background-color 0.2s ease;
}

.add-announcement-btn:hover {
  background-color: #f0f7ff;
}

.add-announcement-btn text {
  font-weight: 500;
}

/* 公告容器样式 */
.announcements-container {
  margin-top: 16rpx;
}

/* 空状态样式 */
.empty-announcements {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx dashed #e5e5e5;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  margin-top: 16rpx;
  text-align: center;
  line-height: 1.4;
}

/* 公告列表样式 */
.announcements-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 公告包装器 - SwipeCell容器 */
.announcement-wrapper {
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  background-color: #ffffff;
  overflow: hidden;
}



/* 公告项内容样式 */
.announcement-item {
  position: relative; /* 为浮动按钮提供定位基准 */
  padding: 24rpx;
  background-color: #ffffff;
}

/* 公告头部信息 */
.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

/* 公告序号 */
.announcement-order {
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
}

/* 公告状态标签 */
.announcement-status {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
  transition: all 0.2s ease; /* 添加过渡动画 */
}

/* 可点击状态标签样式 */
.announcement-status.clickable {
  cursor: pointer;
  user-select: none; /* 防止文本选择 */
}

/* 点击效果 */
.announcement-status.clickable:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.announcement-status.active {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1rpx solid #b7eb8f;
}

.announcement-status.inactive {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1rpx solid #ffccc7;
}

/* 公告内容显示区域（非编辑状态） */
.announcement-content-display {
  min-height: 120rpx;
  margin-bottom: 12rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e5e5e5;
  cursor: pointer;
  transition: all 0.2s ease;
}

.announcement-content-display:hover {
  background-color: #f0f7ff;
  border-color: #d6e4ff;
}

/* 只读文本显示样式 */
.announcement-text-display {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333333;
  word-wrap: break-word;
  white-space: pre-wrap; /* 保持换行和空格 */
  min-height: 88rpx; /* 确保有足够高度显示内容 */
}

/* 空内容时的占位符样式 */
.announcement-text-display:empty::before {
  content: "请输入公告内容";
  color: #999999;
  font-style: italic;
}

/* 公告文本框样式（编辑状态） */
.announcement-textarea {
  min-height: 120rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e5e5e5;
  margin-bottom: 12rpx;
}

/* 编辑状态的文本框 */
.announcement-textarea.editing {
  background-color: #ffffff;
  border-color: #0052d9;
  box-shadow: 0 0 0 2rpx rgba(0, 82, 217, 0.2);
}

/* 禁用状态的文本框 */
.announcement-textarea[disabled] {
  background-color: #f5f5f5;
  color: #999999;
}

/* 公告元信息 */
.announcement-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 22rpx;
  color: #999999;
}

.announcement-time {
  font-size: 22rpx;
  color: #999999;
}

.announcement-length {
  font-size: 22rpx;
  color: #999999;
}

/* ===================================================================
 * 12. 编辑模式浮动按钮样式 - Edit Mode Floating Buttons
 *
 * 编辑模式下显示的确认/取消浮动按钮
 * 采用现代化的浮动设计，提供清晰的操作反馈
 * ================================================================= */

/* 浮动操作按钮容器 */
.edit-floating-actions {
  position: absolute;
  right: 24rpx;
  bottom: 24rpx;
  display: flex;
  gap: 8rpx; /* 减小间距以适应三个按钮 */
  z-index: 10;
}

/* 浮动按钮基础样式 */
.floating-btn {
  display: flex;
  align-items: center;
  gap: 4rpx; /* 减小图标和文字间距 */
  padding: 10rpx 12rpx; /* 减小内边距以适应三个按钮 */
  border-radius: 18rpx;
  font-size: 22rpx; /* 稍微减小字体 */
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  user-select: none;
}

/* 删除按钮样式 */
.delete-btn {
  background-color: #ff4d4f;
  color: #ffffff;
  border: 1rpx solid #ff4d4f;
}

.delete-btn:hover {
  background-color: #ff7875;
  border-color: #ff7875;
}

.delete-btn:active {
  transform: scale(0.95);
  background-color: #d9363e;
}

/* 取消按钮样式 */
.cancel-btn {
  background-color: #ffffff;
  color: #666666;
  border: 1rpx solid #d9d9d9;
}

.cancel-btn:hover {
  background-color: #f5f5f5;
  border-color: #b7b7b7;
}

.cancel-btn:active {
  transform: scale(0.95);
  background-color: #f0f0f0;
}

/* 确认按钮样式 */
.confirm-btn {
  background-color: #0052d9;
  color: #ffffff;
  border: 1rpx solid #0052d9;
}

.confirm-btn:hover {
  background-color: #1765ff;
  border-color: #1765ff;
}

.confirm-btn:active {
  transform: scale(0.95);
  background-color: #003ba3;
}

/* 浮动按钮文字 */
.floating-btn text {
  font-size: 22rpx; /* 与按钮基础样式保持一致 */
  font-weight: 500;
}

/* ===================================================================
 * 样式文件总结 - Style File Summary
 *
 * 这个样式文件实现了一个现代化的系统设置页面，主要特点：
 *
 * 1. 设计系统：
 *    - 统一的颜色规范：主题蓝色#0052d9，灰色系列
 *    - 一致的间距系统：16rpx、24rpx、32rpx的倍数关系
 *    - 现代化的圆角和阴影：营造层次感和现代感
 *
 * 2. 布局系统：
 *    - Flexbox布局：灵活的弹性盒子布局
 *    - 卡片式设计：清晰的信息分组和层次
 *    - 响应式设计：适配不同屏幕尺寸
 *
 * 3. 交互体验：
 *    - 悬停效果：提供视觉反馈（虽然小程序中有限）
 *    - 过渡动画：平滑的状态变化
 *    - 清晰的视觉层次：突出重要信息
 *
 * 4. 技术特点：
 *    - rpx单位：微信小程序的响应式单位
 *    - CSS3特性：渐变、阴影、过渡等现代特性
 *    - 组件样式覆盖：使用!important覆盖第三方组件样式
 *
 * 5. 可维护性：
 *    - 模块化组织：按功能模块分组样式
 *    - 详细注释：每个样式块都有详细说明
 *    - 命名规范：清晰的CSS类名和结构
 *
 * 与您熟悉的技术对比：
 * - 布局方式：类似于WPF的Grid和StackPanel布局
 * - 样式系统：类似于WinForms的外观设置
 * - 响应式设计：类似于Bootstrap的栅格系统
 * - 组件样式：类似于自定义控件的样式模板
 *
 * 学习价值：
 * 这个样式文件展示了如何构建一个专业的移动端设置页面，
 * 包含了现代UI设计、响应式布局、用户体验优化等多个方面。
 * ================================================================= */
